
  /* Page Wrapper */
  #wrapper {
    max-width: 100%;
    background: #ffffff;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  
  /* Header Styling */
  h1 {
    font-family: '<PERSON><PERSON>', Tahoma, sans-serif;
    font-weight: bold;
    font-size: 2em;
    color: #333;
    margin-bottom: 0;
  }
  
  /* Table Styling */
  #keywords {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
    font-size: 1em;
  }
  
  /* Header Styling */
  #keywords thead {
    background: #4b6cb7;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to left, #182848, #4b6cb7);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to left, #182848, #4b6cb7); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */


  }
  
  #keywords thead th {
    font-weight: bold;
    padding: 12px;
    color: #ffffff;
  }
  
  #keywords thead th span {
    position: relative;
    padding-right: 20px;
  }
  
  /* Body Styling */
  #keywords tbody tr {
    border-bottom: 1px solid #e9ecef;
  }
  
  #keywords tbody tr:hover {
    background: #f1f3f5;
  }
  
  #keywords tbody td {
    padding: 12px;
    color: #555;
  }
  
  #keywords tbody td.lalign {
    text-align: left;
  }
  
  /* Table Hover Effect */
  #keywords tbody tr:hover td {
    background-color: #eaf3fa;
    color: #333;
  }
  