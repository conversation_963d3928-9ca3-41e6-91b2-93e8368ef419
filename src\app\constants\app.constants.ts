export const APP_CONSTANTS = {
  // Application info
  APP_NAME: 'Blood Bank Management System',
  APP_VERSION: '1.0.0',

  // Local storage keys
  STORAGE_KEYS: {
    USER_TOKEN: 'userToken',
    USER_DATA: 'userData',
    THEME: 'theme',
  },

  // User types
  USER_TYPES: {
    ADMIN: 'ADMIN',
    DONOR: 'DONAR', // Keep original spelling for API compatibility
    HOSPITAL: 'HOSPITAL',
  } as const,

  // Blood types
  BLOOD_TYPES: [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
  ] as const,

  // Status types
  STATUS: {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    COMPLETED: 'COMPLETED',
    REJECTED: 'REJECTED',
    CANCELLED: 'CANCELLED',
    ACTIVE: 'ACTIVE',
    INACTIVE: 'INACTIVE',
  } as const,

  // Urgency levels
  URGENCY_LEVELS: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
    CRITICAL: 'CRITICAL',
  } as const,

  // Routes
  ROUTES: {
    HOME: '',
    LOGIN: 'login',
    REGISTER: 'DonarReg',
    DONOR_HOME: 'Donar-Home',
    ADMIN: 'Admin',
    HOSPITAL: 'Hospital',
    ADD_HOSPITAL: 'add-Hospital',
    ABOUT: 'aboutus',
  } as const,

  // Validation
  VALIDATION: {
    MIN_AGE: 18,
    MAX_AGE: 65,
    MIN_WEIGHT: 50, // kg
    MIN_PASSWORD_LENGTH: 6,
    PHONE_PATTERN: /^[0-9]{10}$/,
    EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  } as const,
} as const;
