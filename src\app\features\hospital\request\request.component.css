.divider {
    height: 2px;
    background: #c31432;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to left, #240b36, #c31432);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to left, #240b36, #c31432); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
 /* Match with your blue color */
  }
  
  h1 {
    font-size: 2.5rem; /* Adjust size */
  }
  
  h3 {
    font-size: 1.5rem; /* Adjust size */
  }
  
  .btn-primary {
    background-color: #4f6fad; /* Match with your blue color */
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #3b5f8b; /* Darken the button on hover */
  }
  