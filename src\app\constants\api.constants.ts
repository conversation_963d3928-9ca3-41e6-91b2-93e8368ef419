export const API_BASE_URL = 'http://localhost:8080';

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: `${API_BASE_URL}/Authentication/login`,
  },

  // Donor endpoints
  DONOR: {
    BASE: `${API_BASE_URL}/Donar`,
    FIND_BY_ID: (id: string) => `${API_BASE_URL}/Donar/findById/${id}`,
    UPDATE: `${API_BASE_URL}/Donar/update`,
    ADD: `${API_BASE_URL}/Donar/add`,
    DELETE: (id: string) => `${API_BASE_URL}/Donar/delete/${id}`,
  },

  // Hospital endpoints
  HOSPITAL: {
    BASE: `${API_BASE_URL}/Hospital`,
    FIND_BY_ID: (id: string) => `${API_BASE_URL}/Hospital/findById/${id}`,
    UPDATE: `${API_BASE_URL}/Hospital/update`,
    ADD: `${API_BASE_URL}/Hospital/add`,
    DELETE: (id: string) => `${API_BASE_URL}/Hospital/delete/${id}`,
  },

  // Blood Request endpoints
  BLOOD_REQUEST: {
    BASE: `${API_BASE_URL}/BloodRequest`,
    ADD: `${API_BASE_URL}/BloodRequest/add`,
    PENDING: `${API_BASE_URL}/BloodRequest/pending`,
    COMPLETED: `${API_BASE_URL}/BloodRequest/completed`,
    APPROVED: `${API_BASE_URL}/BloodRequest/approved`,
  },

  // Approve Request endpoints
  APPROVE_REQUEST: {
    BASE: `${API_BASE_URL}/ApproveRequest`,
    ADD: `${API_BASE_URL}/ApproveRequest/add`,
    COMPLETED: `${API_BASE_URL}/ApproveRequest/completed`,
  },

  // Inventory endpoints
  INVENTORY: {
    BASE: `${API_BASE_URL}/Inventory`,
    ALL: `${API_BASE_URL}/Inventory/all`,
    UPDATE: `${API_BASE_URL}/Inventory/update`,
  },

  // Feedback endpoints
  FEEDBACK: {
    BASE: `${API_BASE_URL}/FeedBack`,
    ADD: `${API_BASE_URL}/FeedBack/add`,
    ALL: `${API_BASE_URL}/FeedBack/all`,
  },

  // Appointment endpoints
  APPOINTMENT: {
    BASE: `${API_BASE_URL}/Appointment`,
    ADD: `${API_BASE_URL}/Appointment/add`,
    FIND_BY_DONOR: (donorId: string) => `${API_BASE_URL}/Appointment/donor/${donorId}`,
    UPDATE_STATUS: `${API_BASE_URL}/Appointment/updateStatus`,
  },
} as const;
