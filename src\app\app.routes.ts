import { Routes } from '@angular/router';
import { HomePageComponent } from './features/public-pages/home-page/home-page.component';
import { LoginComponent } from './features/auth/login/login.component';
import { RegisterUserPageComponent } from './features/auth/register/register-user-page.component';
import { HomeComponent } from './features/donor/home/<USER>';
import { AdminComponent } from './features/admin/admin.component';
import { HospitalComponent } from './features/hospital/hospital.component';
import { AddHospitalComponent } from './features/admin/add-hospital/add-hospital.component';
import { authGuard } from './core/guards/auth.guard';
import { AboutusComponent } from './features/public-pages/aboutus/aboutus.component';

export const routes: Routes = [
    // {
    //     path:"", 
    //     redirectTo:"/login",
    //     pathMatch:"full"
    // },
    {
        path:'',
        component:HomePageComponent
    },
    {
        path:'login',
        component:LoginComponent
    },
    {
        path:'DonarReg',
        component:RegisterUserPageComponent
    },
    {
        path:'Donar-Home',
        component:HomeComponent,
        canActivate: [authGuard]
    },
    {
        path:'Admin',
        component:AdminComponent,
        canActivate: [authGuard]
    },
    {
        path:'Hospital',
        component:HospitalComponent,
        canActivate: [authGuard]
    },
    {
        path:'add-Hospital',
        component:AddHospitalComponent
    },
    {
        path:'aboutus',
        component:AboutusComponent
    }
];
