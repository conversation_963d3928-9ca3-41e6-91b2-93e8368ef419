<div class="container mt-5 mb-5 " data-aos="fade-right">
  <div class="row text-center mb-4">
    <div class="col">
      <h1 class="fw-bolder ">Blood Request</h1>
      <h3 class="fw-lighter">Fill the form below to Request!</h3>
    </div>
  </div>
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <form class="bg-light p-4 border rounded-5">
        <div class="form-row">
          <div class="form-group col-md-6">
            <label for="hospitalID" class="">Hospital ID</label>
            <input type="text" id="hospitalID" name="hospitalID" class="form-control" [(ngModel)]="temp.hospitalID" required value="{{hospital.hospitalID}}" />
          </div>
          <div class="form-group col-md-6">
            <label for="bloodType" class="">Blood Type</label>
            <select id="bloodType" name="bloodType" class="form-control" required [(ngModel)]="temp.bloodType">
              <option value="">Select Type</option>
              <option value="A+">A+</option>
              <option value="A-">A-</option>
              <option value="B+">B+</option>
              <option value="B-">B-</option>
              <option value="AB+">AB+</option>
              <option value="AB-">AB-</option>
              <option value="O+">O+</option>
              <option value="O-">O-</option>
            </select>
          </div>
          <div class="form-group col-md-6">
            <label for="amount" class="">Blood Amount</label>
            <input type="number" id="amount" name="amount" class="form-control" [(ngModel)]="temp.amount" required />
          </div>
        </div>
        <div class="divider my-3"></div>
        <div class="form-group">
          <label for="message" class="">Message</label>
          <textarea id="message" name="message" class="form-control" cols="46" rows="3" [(ngModel)]="temp.message" placeholder="Your comments..."></textarea>
        </div>
        <button class="btn btn-outline-danger btn-block mt-2" type="button" (click)="addNewRequest()">Submit</button>
      </form>
    </div>
  </div>
</div>
