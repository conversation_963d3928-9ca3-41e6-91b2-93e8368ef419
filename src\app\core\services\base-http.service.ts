import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, retry } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class BaseHttpService {
  protected httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(protected http: HttpClient) {}

  /**
   * Handle HTTP errors
   */
  protected handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error('HTTP Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Generic GET request
   */
  protected get<T>(url: string): Observable<T> {
    return this.http.get<T>(url, this.httpOptions).pipe(
      retry(1),
      catchError(this.handleError)
    );
  }

  /**
   * Generic POST request
   */
  protected post<T>(url: string, data: any): Observable<T> {
    return this.http.post<T>(url, data, this.httpOptions).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Generic PUT request
   */
  protected put<T>(url: string, data: any): Observable<T> {
    return this.http.put<T>(url, data, this.httpOptions).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Generic PATCH request
   */
  protected patch<T>(url: string, data: any): Observable<T> {
    return this.http.patch<T>(url, data, this.httpOptions).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Generic DELETE request
   */
  protected delete<T>(url: string): Observable<T> {
    return this.http.delete<T>(url, this.httpOptions).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * POST request with text response
   */
  protected postText(url: string, data: any): Observable<string> {
    const textOptions = {
      ...this.httpOptions,
      responseType: 'text' as const
    };
    
    return this.http.post(url, data, textOptions).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * PUT request with text response
   */
  protected putText(url: string, data: any): Observable<string> {
    const textOptions = {
      ...this.httpOptions,
      responseType: 'text' as const
    };
    
    return this.http.put(url, data, textOptions).pipe(
      catchError(this.handleError)
    );
  }
}
