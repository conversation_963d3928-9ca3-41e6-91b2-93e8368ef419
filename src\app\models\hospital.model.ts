export interface Hospital {
  hospitalID?: string;
  id?: string;
  name: string;
  email: string;
  password?: string;
  address: string;
  city: string;
  zipCode: string;
  contactNumber: string;
  type: string;
  registrationDate?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'PENDING';
}

export interface BloodRequest {
  requestID?: string;
  hospitalID: string;
  name: string;
  contactNumber: string;
  type: string;
  bloodType: string;
  quantity: number;
  urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  requestDate: string;
  requiredDate: string;
  status: 'PENDING' | 'APPROVED' | 'COMPLETED' | 'REJECTED';
  notes?: string;
}

export interface ApproveRequest {
  approveID?: string;
  donorID: string;
  hospitalID: string;
  appointmentID: string;
  amount: number;
  date: string;
  status: 'COMPLETED' | 'PENDING';
}
