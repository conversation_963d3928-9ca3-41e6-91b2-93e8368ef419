<div class="container-fluid" style="padding-left: 0px;padding-right: 0px;">
    <div class="cover">
        <div class="row pt-4">
            <div class="d-flex justify-content-center mb-1">
                <h1 class="text-center fw-bolder lifeflow-heading">
                    LifeFlow Collection
                    <small class="text-custom">
                        <span class="text-black">by Blood</span>
                        <span class="text-red">Free</span>
                    </small>
                </h1>
            </div>

        </div>
        <div class="row">
            <div class="col-6 col-sm-4 col-lg-3 d-flex justify-content-center mb-1"
                *ngFor="let inventory of inventoryList01">
                <app-qty [inventory]="inventory"></app-qty>
            </div>
        </div>
        <div class="row mt-3 pb-3">
            <div class="col-6 col-sm-4 col-lg-3 d-flex justify-content-center mb-1"
                *ngFor="let inventory of inventoryList02">
                <app-qty [inventory]="inventory"></app-qty>
            </div>
        </div>

        <div class="row pt-5 text-center">
            <h6>Blood can symbolize struggle, violence, and conflict, particularly in expressions such as
                <span class="highlight">"bloodshed"</span> or
                <span class="highlight">"blood feud."</span>
            </h6>
        </div>
    </div>

    <div class="row pt-0">
        <div class="container-fluid backColor pb-4" >
            <div class="d-flex justify-content-center" data-aos="fade-up">
                <h2 class=" text-white fw-bold">Admin Informations</h2>
            </div>
            <div class="row">
                <div class="container pt-1 " data-aos="fade-up">
                    <div class="row d-flex justify-content-center">
                        <div class="col-md-7">
                            <div class="card border border-danger p-1 py-4">
                                <!-- Added border and border-dark classes -->
                                <div class="text-center">
                                    <img src="https://i.imgur.com/bDLhJiP.jpg" width="100" class="rounded-circle" />
                                </div>

                                <div class="text-center mt-3">
                                    <span class="backColor p-1 px-4 rounded text-white fw-bolder">Admin ID
                                        {{admin.adminID}}</span>
                                    <h5 class="mt-2 mb-0 fw-bold">Name: {{ admin.name }}</h5>
                                    <span class="fw-bold">Email: {{ admin.email }} , </span>
                                    <span class="fw-bold">Number: {{ admin.contactNumber }}</span>

                                    <div class="buttons mt-2">
                                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal"
                                            data-bs-target="#exampleModal" (click)="updateAdminDetails(admin)">
                                            Edit
                                        </button>

                                        <div class="modal fade" id="exampleModal" tabindex="-1"
                                            aria-labelledby="exampleModalLabel" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h1 class="modal-title fs-5" id="exampleModalLabel">
                                                            Update Your Details
                                                        </h1>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                            aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <form>
                                                            <div class="mb-1">
                                                                <label for="donorID" class="col-form-label">Admin
                                                                    ID</label>
                                                                <input disabled type="text" class="form-control"
                                                                    name="donorID" required
                                                                    [(ngModel)]="temp.adminID" />
                                                            </div>
                                                            <div class="mb-1">
                                                                <label for="name" class="col-form-label">Name</label>
                                                                <input type="text" class="form-control" id="name"
                                                                    name="name" required [(ngModel)]="temp.name" />
                                                            </div>
                                                            <div class="mb-1">
                                                                <label for="email" class="col-form-label">Email</label>
                                                                <input type="email" class="form-control" id="email"
                                                                    name="email" required [(ngModel)]="temp.email" />
                                                            </div>
                                                            <div class="mb-1">
                                                                <label for="contactNumber"
                                                                    class="col-form-label">Contact Number</label>
                                                                <input type="text" class="form-control"
                                                                    id="contactNumber" name="contactNumber" required
                                                                    [(ngModel)]="temp.contactNumber" />
                                                            </div>
                                                        </form>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary"
                                                            data-bs-dismiss="modal">
                                                            Close
                                                        </button>
                                                        <button type="submit" class="btn btn-primary"
                                                            (click)="saveAdmin()" data-bs-dismiss="modal">
                                                            Save Changes
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <button class="btn btn-outline-danger fw-bold px-4 ms-3"
                                            (click)="deleteAdmin(admin.adminID)">
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div>
            <h2 class="text-black fw-bolder text-center">Appointment Manager</h2>
        </div>
        <app-pending-list [appointments]="appointmentList"></app-pending-list>
    </div>
    <div class="row">

    </div>
    <div class="row">


    </div>
</div>