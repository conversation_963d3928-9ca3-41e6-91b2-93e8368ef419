<header>
    <nav class="navbar navbar-expand-md sticky-top" role="navigation">
        <a class="navbar-brand" href="#" >
            <div class="semi-circle">
                <img [src]="ASSETS.IMAGES.LOGO" alt="Blood Bank Logo" class="half-square-circle-logo">
            </div>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarContent">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link" href="#">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Blood</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">Donation</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link"  routerLink="/aboutus" href="#">Contact us</a>
                </li>
            </ul>
            <div class="ml-auto" style="margin-right: 15px;">
                <button routerLink="/login" *ngIf="showDonationButton" class="btn btn-outline-light me-2 fw-bold" type="button">Donate Now</button>
                <button routerLink="/login" *ngIf="showSignUpButton" class="btn btn-light hovering fw-bold " type="button" (click)="toggleSignIn()">{{ isSignedIn ? 'Sign Out' : 'Sign Up' }}</button>
            </div>
        </div>
    </nav>
</header>
