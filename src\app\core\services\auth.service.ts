import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { LoginResponse, LoginRequest, User } from '../../models';
import { API_ENDPOINTS, APP_CONSTANTS } from '../../constants';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private loggedIn = new BehaviorSubject<boolean>(this.checkToken());
  private currentUser = new BehaviorSubject<User | null>(this.getCurrentUser());

  constructor(
    private http: HttpClient,
    private router: Router
  ) {}

  /**
   * Login user with email and password
   */
  login(email: string, password: string): Observable<LoginResponse> {
    const loginRequest: LoginRequest = { email, password };
    
    return this.http.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, loginRequest).pipe(
      tap(response => {
        if (response && response.id) {
          this.setUserSession(response);
        }
      }),
      catchError(error => {
        console.error('Login error:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): Observable<boolean> {
    return this.loggedIn.asObservable();
  }

  /**
   * Get current user observable
   */
  getCurrentUser$(): Observable<User | null> {
    return this.currentUser.asObservable();
  }

  /**
   * Get current user synchronously
   */
  getCurrentUserSync(): User | null {
    return this.currentUser.value;
  }

  /**
   * Logout user and clear session
   */
  logout(): void {
    this.clearUserSession();
    this.router.navigate([APP_CONSTANTS.ROUTES.LOGIN]);
  }

  /**
   * Get user type
   */
  getUserType(): string | null {
    const user = this.getCurrentUserSync();
    return user ? user.type : null;
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const userType = this.getUserType();
    return userType === role;
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.hasRole(APP_CONSTANTS.USER_TYPES.ADMIN);
  }

  /**
   * Check if user is donor
   */
  isDonor(): boolean {
    return this.hasRole(APP_CONSTANTS.USER_TYPES.DONOR);
  }

  /**
   * Check if user is hospital
   */
  isHospital(): boolean {
    return this.hasRole(APP_CONSTANTS.USER_TYPES.HOSPITAL);
  }

  /**
   * Set user session data
   */
  private setUserSession(response: LoginResponse): void {
    const user: User = {
      id: response.id,
      email: '', // Will be set from login form
      type: response.type as 'ADMIN' | 'DONAR' | 'HOSPITAL'
    };

    localStorage.setItem(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN, response.id);
    localStorage.setItem(APP_CONSTANTS.STORAGE_KEYS.USER_DATA, JSON.stringify(user));
    
    this.loggedIn.next(true);
    this.currentUser.next(user);
  }

  /**
   * Clear user session data
   */
  private clearUserSession(): void {
    localStorage.removeItem(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN);
    localStorage.removeItem(APP_CONSTANTS.STORAGE_KEYS.USER_DATA);
    
    this.loggedIn.next(false);
    this.currentUser.next(null);
  }

  /**
   * Check if token exists in localStorage
   */
  private checkToken(): boolean {
    return !!localStorage.getItem(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN);
  }

  /**
   * Get current user from localStorage
   */
  private getCurrentUser(): User | null {
    const userData = localStorage.getItem(APP_CONSTANTS.STORAGE_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  }
}
