import { APP_CONSTANTS } from '../constants';

/**
 * Validation utility functions
 */

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  return APP_CONSTANTS.VALIDATION.EMAIL_PATTERN.test(email);
}

/**
 * Validate phone number format
 */
export function isValidPhoneNumber(phone: string): boolean {
  return APP_CONSTANTS.VALIDATION.PHONE_PATTERN.test(phone);
}

/**
 * Validate password strength
 */
export function isValidPassword(password: string): boolean {
  return password.length >= APP_CONSTANTS.VALIDATION.MIN_PASSWORD_LENGTH;
}

/**
 * Validate age for blood donation
 */
export function isValidDonorAge(age: number): boolean {
  return age >= APP_CONSTANTS.VALIDATION.MIN_AGE && age <= APP_CONSTANTS.VALIDATION.MAX_AGE;
}

/**
 * Validate weight for blood donation
 */
export function isValidDonorWeight(weight: number): boolean {
  return weight >= APP_CONSTANTS.VALIDATION.MIN_WEIGHT;
}

/**
 * Validate blood type
 */
export function isValidBloodType(bloodType: string): boolean {
  return APP_CONSTANTS.BLOOD_TYPES.includes(bloodType as any);
}

/**
 * Check if string is not empty or whitespace
 */
export function isNotEmpty(value: string): boolean {
  return value && value.trim().length > 0;
}

/**
 * Check if value is a positive number
 */
export function isPositiveNumber(value: number): boolean {
  return value > 0;
}

/**
 * Validate required fields in an object
 */
export function validateRequiredFields(obj: any, requiredFields: string[]): string[] {
  const missingFields: string[] = [];
  
  requiredFields.forEach(field => {
    if (!obj[field] || (typeof obj[field] === 'string' && !isNotEmpty(obj[field]))) {
      missingFields.push(field);
    }
  });
  
  return missingFields;
}

/**
 * Get validation error message
 */
export function getValidationErrorMessage(field: string, type: string): string {
  const messages: { [key: string]: { [key: string]: string } } = {
    email: {
      required: 'Email is required',
      invalid: 'Please enter a valid email address'
    },
    password: {
      required: 'Password is required',
      minLength: `Password must be at least ${APP_CONSTANTS.VALIDATION.MIN_PASSWORD_LENGTH} characters long`
    },
    phone: {
      required: 'Phone number is required',
      invalid: 'Please enter a valid 10-digit phone number'
    },
    age: {
      required: 'Age is required',
      invalid: `Age must be between ${APP_CONSTANTS.VALIDATION.MIN_AGE} and ${APP_CONSTANTS.VALIDATION.MAX_AGE} years`
    },
    weight: {
      required: 'Weight is required',
      invalid: `Weight must be at least ${APP_CONSTANTS.VALIDATION.MIN_WEIGHT} kg`
    },
    bloodType: {
      required: 'Blood type is required',
      invalid: 'Please select a valid blood type'
    }
  };

  return messages[field]?.[type] || `${field} is invalid`;
}
