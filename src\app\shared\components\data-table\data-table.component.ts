import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  type?: 'text' | 'date' | 'number' | 'badge' | 'actions';
  width?: string;
}

export interface TableAction {
  label: string;
  icon?: string;
  class?: string;
  action: string;
}

@Component({
  selector: 'app-data-table',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="table-container">
      <!-- Search and filters -->
      <div class="table-header" *ngIf="searchable || filterable">
        <div class="search-box" *ngIf="searchable">
          <input 
            type="text" 
            class="form-control" 
            placeholder="Search..." 
            [(ngModel)]="searchTerm"
            (input)="onSearch()">
        </div>
      </div>

      <!-- Table -->
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th *ngFor="let column of columns" 
                  [style.width]="column.width"
                  [class.sortable]="column.sortable"
                  (click)="onSort(column)">
                {{ column.label }}
                <span *ngIf="column.sortable && sortColumn === column.key" class="sort-indicator">
                  <i class="bi" [ngClass]="sortDirection === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down'"></i>
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of paginatedData; let i = index">
              <td *ngFor="let column of columns">
                <ng-container [ngSwitch]="column.type">
                  <!-- Text -->
                  <span *ngSwitchCase="'text'">{{ getNestedValue(item, column.key) }}</span>
                  
                  <!-- Date -->
                  <span *ngSwitchCase="'date'">{{ getNestedValue(item, column.key) | date:'short' }}</span>
                  
                  <!-- Number -->
                  <span *ngSwitchCase="'number'">{{ getNestedValue(item, column.key) | number }}</span>
                  
                  <!-- Badge -->
                  <span *ngSwitchCase="'badge'" 
                        class="badge" 
                        [ngClass]="getBadgeClass(getNestedValue(item, column.key))">
                    {{ getNestedValue(item, column.key) }}
                  </span>
                  
                  <!-- Actions -->
                  <div *ngSwitchCase="'actions'" class="action-buttons">
                    <button *ngFor="let action of actions" 
                            type="button"
                            class="btn btn-sm"
                            [ngClass]="action.class || 'btn-outline-primary'"
                            (click)="onAction(action.action, item)">
                      <i *ngIf="action.icon" class="bi" [ngClass]="action.icon"></i>
                      {{ action.label }}
                    </button>
                  </div>
                  
                  <!-- Default -->
                  <span *ngSwitchDefault>{{ getNestedValue(item, column.key) }}</span>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="pagination-container" *ngIf="pageable && totalPages > 1">
        <nav>
          <ul class="pagination">
            <li class="page-item" [class.disabled]="currentPage === 1">
              <button class="page-link" (click)="goToPage(currentPage - 1)">Previous</button>
            </li>
            <li class="page-item" 
                *ngFor="let page of getPageNumbers()" 
                [class.active]="page === currentPage">
              <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
            </li>
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <button class="page-link" (click)="goToPage(currentPage + 1)">Next</button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  `,
  styles: [`
    .table-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .table-header {
      padding: 1rem;
      border-bottom: 1px solid #dee2e6;
    }

    .search-box {
      max-width: 300px;
    }

    .table {
      margin-bottom: 0;
    }

    .sortable {
      cursor: pointer;
      user-select: none;
    }

    .sortable:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .sort-indicator {
      margin-left: 0.5rem;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .badge.bg-success { background-color: #198754 !important; }
    .badge.bg-warning { background-color: #ffc107 !important; color: #000; }
    .badge.bg-danger { background-color: #dc3545 !important; }
    .badge.bg-info { background-color: #0dcaf0 !important; color: #000; }

    .pagination-container {
      padding: 1rem;
      display: flex;
      justify-content: center;
    }

    .pagination {
      margin: 0;
    }
  `]
})
export class DataTableComponent implements OnInit {
  @Input() data: any[] = [];
  @Input() columns: TableColumn[] = [];
  @Input() actions: TableAction[] = [];
  @Input() searchable: boolean = true;
  @Input() filterable: boolean = false;
  @Input() pageable: boolean = true;
  @Input() pageSize: number = 10;

  @Output() actionClicked = new EventEmitter<{action: string, item: any}>();

  searchTerm: string = '';
  sortColumn: string = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  currentPage: number = 1;
  filteredData: any[] = [];
  paginatedData: any[] = [];

  ngOnInit() {
    this.filteredData = [...this.data];
    this.updatePaginatedData();
  }

  get totalPages(): number {
    return Math.ceil(this.filteredData.length / this.pageSize);
  }

  onSearch(): void {
    if (!this.searchTerm) {
      this.filteredData = [...this.data];
    } else {
      this.filteredData = this.data.filter(item =>
        this.columns.some(column =>
          String(this.getNestedValue(item, column.key))
            .toLowerCase()
            .includes(this.searchTerm.toLowerCase())
        )
      );
    }
    this.currentPage = 1;
    this.updatePaginatedData();
  }

  onSort(column: TableColumn): void {
    if (!column.sortable) return;

    if (this.sortColumn === column.key) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column.key;
      this.sortDirection = 'asc';
    }

    this.filteredData.sort((a, b) => {
      const aVal = this.getNestedValue(a, column.key);
      const bVal = this.getNestedValue(b, column.key);
      
      let comparison = 0;
      if (aVal > bVal) comparison = 1;
      if (aVal < bVal) comparison = -1;
      
      return this.sortDirection === 'asc' ? comparison : -comparison;
    });

    this.updatePaginatedData();
  }

  onAction(action: string, item: any): void {
    this.actionClicked.emit({ action, item });
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedData();
    }
  }

  getPageNumbers(): number[] {
    const pages = [];
    const maxVisible = 5;
    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(this.totalPages, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  }

  private updatePaginatedData(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedData = this.filteredData.slice(startIndex, endIndex);
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((o, p) => o && o[p], obj);
  }

  private getBadgeClass(value: string): string {
    const statusClasses: { [key: string]: string } = {
      'ACTIVE': 'bg-success',
      'COMPLETED': 'bg-success',
      'PENDING': 'bg-warning',
      'APPROVED': 'bg-info',
      'REJECTED': 'bg-danger',
      'CANCELLED': 'bg-danger',
      'INACTIVE': 'bg-secondary'
    };
    return statusClasses[value?.toUpperCase()] || 'bg-secondary';
  }
}
