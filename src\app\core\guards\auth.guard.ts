import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { map, tap } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { APP_CONSTANTS } from '../../constants';

/**
 * Auth guard to protect routes that require authentication
 */
export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  return authService.isLoggedIn().pipe(
    tap(isLoggedIn => {
      if (!isLoggedIn) {
        console.warn('Access denied. User not authenticated.');
        router.navigate([APP_CONSTANTS.ROUTES.LOGIN], {
          queryParams: { returnUrl: state.url }
        });
      }
    }),
    map(isLoggedIn => isLoggedIn)
  );
};

/**
 * Role-based guard factory
 */
export const roleGuard = (allowedRoles: string[]): CanActivateFn => {
  return (route, state) => {
    const authService = inject(AuthService);
    const router = inject(Router);

    return authService.isLoggedIn().pipe(
      map(isLoggedIn => {
        if (!isLoggedIn) {
          router.navigate([APP_CONSTANTS.ROUTES.LOGIN], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        }

        const userType = authService.getUserType();
        const hasPermission = userType && allowedRoles.includes(userType);

        if (!hasPermission) {
          console.warn(`Access denied. User role '${userType}' not in allowed roles:`, allowedRoles);
          router.navigate([APP_CONSTANTS.ROUTES.HOME]);
          return false;
        }

        return true;
      })
    );
  };
};

/**
 * Admin only guard
 */
export const adminGuard: CanActivateFn = roleGuard([APP_CONSTANTS.USER_TYPES.ADMIN]);

/**
 * Donor only guard
 */
export const donorGuard: CanActivateFn = roleGuard([APP_CONSTANTS.USER_TYPES.DONOR]);

/**
 * Hospital only guard
 */
export const hospitalGuard: CanActivateFn = roleGuard([APP_CONSTANTS.USER_TYPES.HOSPITAL]);
