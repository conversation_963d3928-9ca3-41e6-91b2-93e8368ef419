html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
  }
  body {
    background: #f3f4f6;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    color: #444;
    font-size: 14px;
    line-height: 1.6;
    padding: 10px;
  }
  
  /* Page Wrapper */
  #wrapper {
    max-width: 100%;
    background: #ffffff;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  
  /* Header Styling */
  h1 {
    font-family: 'Amarante', Tahoma, sans-serif;
    font-weight: bold;
    font-size: 2em;
    color: #333;
    margin-bottom: 0;
  }
  
  /* Table Styling */
  #keywords {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
    font-size: 1em;
  }
  
  /* Header Styling */
  #keywords thead {
    background: #b8daff;
  }
  
  #keywords thead th {
    font-weight: bold;
    padding: 12px;
    color: #2a4d69;
  }
  
  #keywords thead th span {
    position: relative;
    padding-right: 20px;
  }
  
  /* Body Styling */
  #keywords tbody tr {
    border-bottom: 1px solid #e9ecef;
  }
  
  #keywords tbody tr:hover {
    background: #f1f3f5;
  }
  
  #keywords tbody td {
    padding: 12px;
    color: #555;
  }
  
  #keywords tbody td.lalign {
    text-align: left;
  }
  
  /* Table Hover Effect */
  #keywords tbody tr:hover td {
    background-color: #eaf3fa;
    color: #333;
  }
  


  .lifeflow-heading {
    font-family: 'Arial', sans-serif;
    font-size: 2.4rem; /* Base size for large screens */
    background: linear-gradient(to right, #dd1818, #ff4b2b); /* Red gradient */
    -webkit-background-clip: text;
    color: transparent;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
    animation: fadeIn 1.5s ease-in-out;
}

.text-custom {
    font-size: 1rem; /* Base font size for smaller text */
    color: inherit; /* Inherit parent text color */
    font-weight: normal; /* Keep weight normal for the small tag */
}

.text-black {
    color: black; /* Make 'Blood' black */
}

.text-red {
    color: #dd1818; /* Make 'Free' red */
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .lifeflow-heading {
        font-size: 2.5rem; /* Adjust for smaller screens */
    }
    .text-custom {
        font-size: 0.9rem; /* Adjust small text size for smaller screens */
    }
}


.cover {
  background-image: url('/bloodfree.jpg'); /* Replace with your image path */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 95vh; /* Ensures the container takes up full viewport height */
  width: 100%; 
}

.highlight {
  text-decoration: underline;
  font-weight: bold;
}


.colColor1{
  background: #EF3B36;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to top, #FFFFFF, #EF3B36);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to top, #FFFFFF, #EF3B36); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

}

.backColor{
  color: white;
  background: #c31432;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to left, #240b36, #c31432);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to left, #240b36, #c31432); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */



}
