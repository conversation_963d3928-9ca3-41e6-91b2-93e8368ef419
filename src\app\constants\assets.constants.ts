/**
 * Asset path constants for easier asset management
 */

const ASSETS_BASE = 'assets';

export const ASSETS = {
  IMAGES: {
    LOGO: `${ASSETS_BASE}/images/logo.png`,
    PROFILE: `${ASSETS_BASE}/images/profile.webp`,
    RED_CROSS: `${ASSETS_BASE}/images/redCross.png`,
    BANNER: `${ASSETS_BASE}/images/banner.webp`,
    
    // Background images
    BACKGROUND: `${ASSETS_BASE}/images/background.jpg`,
    BACKGROUND_2: `${ASSETS_BASE}/images/background2.png`,
    BACKGROUND_3: `${ASSETS_BASE}/images/backgroud3.jpg`,
    DONOR_BACKGROUND: `${ASSETS_BASE}/images/backdonar.jpg`,
    REGISTER_BACKGROUND: `${ASSETS_BASE}/images/regback.jpg`,
    
    // Card images
    CARD_1: `${ASSETS_BASE}/images/card1.jpg`,
    CARD_2: `${ASSETS_BASE}/images/card2.jpg`,
    CARD_3: `${ASSETS_BASE}/images/card3.jpg`,
    
    // Other images
    ABOUT: `${ASSETS_BASE}/images/about.jpg`,
    BLOOD_FREE: `${ASSETS_BASE}/images/bloodfree.jpg`,
    FORWARD: `${ASSETS_BASE}/images/Forward.png`,
    ARROW: `${ASSETS_BASE}/images/arrow.png`,
  },
  
  VIDEOS: {
    ANIMATION: `${ASSETS_BASE}/videos/animation.mp4`,
    HEART: `${ASSETS_BASE}/videos/heart.webm`,
    REGISTRATION: `${ASSETS_BASE}/videos/reg.webm`,
  },
  
  ICONS: {
    FAVICON: `${ASSETS_BASE}/icons/favicon.ico`,
  }
} as const;

/**
 * Helper function to get asset path
 */
export function getAssetPath(category: keyof typeof ASSETS, name: string): string {
  const categoryAssets = ASSETS[category] as any;
  return categoryAssets[name] || '';
}

/**
 * Blood type icons (using Bootstrap Icons)
 */
export const BLOOD_TYPE_ICONS = {
  'A+': 'bi-droplet-fill text-danger',
  'A-': 'bi-droplet text-danger',
  'B+': 'bi-droplet-fill text-primary',
  'B-': 'bi-droplet text-primary',
  'AB+': 'bi-droplet-fill text-success',
  'AB-': 'bi-droplet text-success',
  'O+': 'bi-droplet-fill text-warning',
  'O-': 'bi-droplet text-warning',
} as const;

/**
 * Status icons
 */
export const STATUS_ICONS = {
  PENDING: 'bi-clock text-warning',
  APPROVED: 'bi-check-circle text-success',
  COMPLETED: 'bi-check-circle-fill text-success',
  REJECTED: 'bi-x-circle text-danger',
  CANCELLED: 'bi-x-circle-fill text-danger',
  ACTIVE: 'bi-circle-fill text-success',
  INACTIVE: 'bi-circle text-secondary',
} as const;
