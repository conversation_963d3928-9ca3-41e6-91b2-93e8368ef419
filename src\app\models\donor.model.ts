export interface Donor {
  donorID?: string;
  id?: string;
  name: string;
  email: string;
  password?: string;
  address: string;
  city: string;
  contactNumber: string;
  bloodType: string;
  age: number;
  weight: number;
  lastDonationDate?: string;
  eligibilityStatus?: 'ELIGIBLE' | 'NOT_ELIGIBLE';
}

export interface DonorHistory {
  recordID: string;
  donorID: string;
  hospitalID: string;
  donationDate: string;
  amount: number;
}

export interface Appointment {
  appointmentID: string;
  donorID: string;
  hospitalID: string;
  appointmentDate: string;
  appointmentTime: string;
  status: 'PENDING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
}
