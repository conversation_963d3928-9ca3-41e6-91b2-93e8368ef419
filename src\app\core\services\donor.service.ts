import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseHttpService } from './base-http.service';
import { Donor, DonorHistory, Appointment } from '../../models';
import { API_ENDPOINTS } from '../../constants';

@Injectable({
  providedIn: 'root'
})
export class DonorService extends BaseHttpService {

  /**
   * Get donor by ID
   */
  getDonorById(id: string): Observable<Donor> {
    return this.get<Donor>(API_ENDPOINTS.DONOR.FIND_BY_ID(id));
  }

  /**
   * Add new donor
   */
  addDonor(donor: Donor): Observable<string> {
    return this.postText(API_ENDPOINTS.DONOR.ADD, donor);
  }

  /**
   * Update donor information
   */
  updateDonor(donor: Partial<Donor>): Observable<string> {
    return this.putText(API_ENDPOINTS.DONOR.UPDATE, donor);
  }

  /**
   * Delete donor
   */
  deleteDonor(id: string): Observable<string> {
    return this.delete<string>(API_ENDPOINTS.DONOR.DELETE(id));
  }

  /**
   * Get donor history
   */
  getDonorHistory(donorId: string): Observable<DonorHistory[]> {
    // This endpoint might need to be added to the backend
    return this.get<DonorHistory[]>(`${API_ENDPOINTS.DONOR.BASE}/history/${donorId}`);
  }

  /**
   * Get donor appointments
   */
  getDonorAppointments(donorId: string): Observable<Appointment[]> {
    return this.get<Appointment[]>(API_ENDPOINTS.APPOINTMENT.FIND_BY_DONOR(donorId));
  }

  /**
   * Schedule appointment
   */
  scheduleAppointment(appointment: Appointment): Observable<string> {
    return this.postText(API_ENDPOINTS.APPOINTMENT.ADD, appointment);
  }

  /**
   * Update appointment status
   */
  updateAppointmentStatus(appointmentId: string, status: string): Observable<string> {
    return this.putText(API_ENDPOINTS.APPOINTMENT.UPDATE_STATUS, {
      appointmentId,
      status
    });
  }

  /**
   * Check donor eligibility
   */
  checkEligibility(donorId: string): Observable<{ eligible: boolean; reason?: string }> {
    // This endpoint might need to be added to the backend
    return this.get<{ eligible: boolean; reason?: string }>(`${API_ENDPOINTS.DONOR.BASE}/eligibility/${donorId}`);
  }
}
