html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
  }
  body {
    background: #f3f4f6;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    color: #444;
    font-size: 14px;
    line-height: 1.6;
    padding: 10px;
  }
  
  /* Page Wrapper */
  #wrapper {
    max-width: 100%;
    background: #ffffff;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  
  /* Header Styling */
  h1 {
    font-family: 'Amarante', Tahoma, sans-serif;
    font-weight: bold;
    font-size: 2em;
    color: #333;
    margin-bottom: 0;
  }
  
  /* Table Styling */
  #keywords {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
    font-size: 1em;
  }
  
  /* Header Styling */
  #keywords thead {
    background: #b8daff;
  }
  
  #keywords thead th {
    font-weight: bold;
    padding: 12px;
    color: #2a4d69;
  }
  
  #keywords thead th span {
    position: relative;
    padding-right: 20px;
  }
  
  /* Body Styling */
  #keywords tbody tr {
    border-bottom: 1px solid #e9ecef;
  }
  
  #keywords tbody tr:hover {
    background: #f1f3f5;
  }
  
  #keywords tbody td {
    padding: 12px;
    color: #555;
  }
  
  #keywords tbody td.lalign {
    text-align: left;
  }
  
  /* Table Hover Effect */
  #keywords tbody tr:hover td {
    background-color: #eaf3fa;
    color: #333;
  }
  