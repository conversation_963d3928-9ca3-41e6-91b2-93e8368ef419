import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseHttpService } from './base-http.service';
import { Feedback } from '../../models';
import { API_ENDPOINTS } from '../../constants';

@Injectable({
  providedIn: 'root'
})
export class FeedbackService extends BaseHttpService {

  /**
   * Submit feedback
   */
  submitFeedback(feedback: Feedback): Observable<string> {
    return this.postText(API_ENDPOINTS.FEEDBACK.ADD, feedback);
  }

  /**
   * Get all feedback
   */
  getAllFeedback(): Observable<Feedback[]> {
    return this.get<Feedback[]>(API_ENDPOINTS.FEEDBACK.ALL);
  }

  /**
   * Get feedback by category
   */
  getFeedbackByCategory(category: string): Observable<Feedback[]> {
    // This endpoint might need to be added to the backend
    return this.get<Feedback[]>(`${API_ENDPOINTS.FEEDBACK.BASE}/category/${category}`);
  }

  /**
   * Get feedback by user
   */
  getFeedbackByUser(userId: string): Observable<Feedback[]> {
    // This endpoint might need to be added to the backend
    return this.get<Feedback[]>(`${API_ENDPOINTS.FEEDBACK.BASE}/user/${userId}`);
  }

  /**
   * Get feedback statistics
   */
  getFeedbackStats(): Observable<any> {
    // This endpoint might need to be added to the backend
    return this.get<any>(`${API_ENDPOINTS.FEEDBACK.BASE}/stats`);
  }
}
