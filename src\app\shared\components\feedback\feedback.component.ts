import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FeedbackService } from '../../../core/services/feedback.service';
import { Feedback } from '../../../models/feedback.model';
import { getCurrentDate, getCurrentTime } from '../../../utils/date.utils';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-feedback',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './feedback.component.html',
  styleUrls: ['./feedback.component.css']  // Fix typo from `styleUrl` to `styleUrls`
})
export class FeedbackComponent implements OnInit {
  public text: string = '';

  constructor(private feedbackService: FeedbackService) {}

  ngOnInit(): void {}

  submitTheFeedBack() {
    if (!this.text.trim()) {
      Swal.fire({
        title: "Error",
        text: "Please enter your feedback before submitting.",
        icon: "error"
      });
      return;
    }

    const feedback: Feedback = {
      text: this.text,
      date: getCurrentDate(),
      time: getCurrentTime()
    };

    this.feedbackService.submitFeedback(feedback).subscribe({
      next: (response) => {
        Swal.fire({
          title: "Done",
          text: "Feedback submitted successfully!",
          icon: "success"
        });
        this.text = "";
      },
      error: (error) => {
        console.error("Error submitting feedback:", error);
        Swal.fire({
          title: "Error",
          text: "Failed to submit feedback. Please try again.",
          icon: "error"
        });
      }
    });
  }
}
