
.full-background {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.carousel-item {
    position: relative;
}

.carousel-item::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.603), rgba(0, 0, 0, 0.856)); 
    z-index: 1;
}

.carousel-item img {
    object-fit: cover;
    width: 100%;
    height: 100vh;
    position: relative;
    z-index: 0; /* Keeps image behind the overlay */
}
.carousel-caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    z-index: 2; 
    text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.7);
}
.carousel-caption h1 {
    font-size: 4rem;
    font-weight: bold;
}


.buttonCall{
  background: #ED213A;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #93291E, #ED213A);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #93291E, #ED213A); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  cursor: pointer;
  transition: background-color 0.3s, transform 0.3s;
  color: beige;
}




@media (max-width: 768px) {
    .carousel-caption h1 {
        font-size: 2rem; /* Adjust for smaller screens */
    }
}

/* ANIMATIONS */

@keyframes fadeInUp {
    from {
      opacity: 0;             
      transform: translateY(20px); 
    }
    to {
      opacity: 1;             
      transform: translateY(0); 
    }
  }
  
  .animated-text {
    animation: fadeInUp 2s ease-in-out; 
  }

  @keyframes fadeInDown {
    from {
      opacity: 0;                  /* Start with opacity 0 */
      transform: translateY(-20px); /* Start slightly above */
    }
    to {
      opacity: 1;                  /* End with full opacity */
      transform: translateY(0);    /* End at its original position */
    }
  }
  
  .animated-text-fadeDown {
    animation: fadeInDown 2s ease-in-out; /* Apply fadeInDown animation */
  }
  
  