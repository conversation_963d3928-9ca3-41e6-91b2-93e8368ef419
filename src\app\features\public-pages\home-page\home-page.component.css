

.content {
    width: 98.3%;
    height: 2rem;
    background: #c31432;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to right, #240b36, #c31432);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to right, #240b36, #c31432);
    text-align: start;
    color: #dee2e6;
    display: flex;
    align-items: center; /* Centers items vertically */
    gap: 0.5rem; /* Adds space between the headers, optional */
}

.content h6,
.content .icon {
    margin: 0; /* Remove default margin */
    font-size: 1rem; /* Adjust as needed */
    height: 1rem; /* Set height for the icon */
    width: auto; /* Adjust as needed */
}

.content .icon {
    height: 1.5rem; /* Adjust the height of the icon as needed */
    width: auto; /* Keep the aspect ratio */
}

.col-lg-6 {
    height: 100vh; /* Adjust to desired height */
}
@media (max-width: 767.98px) { /* Small screens (Bootstrap 'sm' breakpoint) */
    .col-lg-6 {
        height: 40vh; /* Adjusted height for small screens */
    }
    .content{
        width: 93.8%;
    }
}
.list {
    background-color: #f8f9fa; /* Light background */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    margin: 1px; /* Space around the list */
}

.list h2 {
    color: #007bff; /* Blue color for the header */
}

.list-group-item {
    background-color: #ffffff; /* White background for items */
    border: 1px solid #dee2e6; /* Border for items */
    border-radius: 4px; /* Rounded corners for items */
    margin-bottom: 5px; /* Space between items */
    transition: background-color 0.3s; /* Smooth background transition */
}

.list-group-item:hover {
    background-color: #FFC7C7; /* Light grey on hover */
}

/* Optional CSS to adjust text style */
h2 span {
    font-weight: bold; /* Makes the words bold */
  }
  
  /* Optional: Adjust the subtitle color */
  p.text-muted {
    font-size: 1.2rem; /* Adjust the font size */
    color: #6c757d; /* Lighter grey for subtitle */
  }
  