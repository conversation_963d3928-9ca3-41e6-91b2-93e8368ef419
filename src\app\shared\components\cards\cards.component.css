.banner{
  background: #EF3B36;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to top, #FFFFFF, #EF3B36);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to top, #FFFFFF, #EF3B36); 

}
.image-container {
    overflow: visible; /* Allow image to overflow container */
  }
  
  .card-img-top {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .card-img-top:hover {
    transform: translateY(-15px) scale(1.1); /* Image pop-out effect */
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3); /* Shadow effect */
    z-index: 10; /* Bring image in front */
    position: relative; /* Ensure z-index works */
  }
  h6{
    cursor: pointer;
  }
  
  /* Media query for small screens */
  @media (max-width: 767px) {
    .card {
      width: 100% !important;  /* Full width for smaller screens */
      max-width: 100%;
      margin: 10px; /* Margin for spacing */
    }
  
    .card-img-top {
      height: 150px; /* Smaller height for the image */
      object-fit: cover; /* Keep aspect ratio */
    }
  
    .card-body {
      min-height: 120px; /* Smaller card body height */
    }
  }
  