import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseHttpService } from './base-http.service';
import { BloodInventory, InventoryUpdate } from '../../models';
import { API_ENDPOINTS } from '../../constants';

@Injectable({
  providedIn: 'root'
})
export class InventoryService extends BaseHttpService {

  /**
   * Get all inventory items
   */
  getAllInventory(): Observable<BloodInventory[]> {
    return this.get<BloodInventory[]>(API_ENDPOINTS.INVENTORY.ALL);
  }

  /**
   * Update inventory item
   */
  updateInventory(update: InventoryUpdate): Observable<string> {
    return this.putText(API_ENDPOINTS.INVENTORY.UPDATE, update);
  }

  /**
   * Get inventory by blood type
   */
  getInventoryByBloodType(bloodType: string): Observable<BloodInventory[]> {
    // This endpoint might need to be added to the backend
    return this.get<BloodInventory[]>(`${API_ENDPOINTS.INVENTORY.BASE}/bloodType/${bloodType}`);
  }

  /**
   * Get inventory by hospital
   */
  getInventoryByHospital(hospitalId: string): Observable<BloodInventory[]> {
    // This endpoint might need to be added to the backend
    return this.get<BloodInventory[]>(`${API_ENDPOINTS.INVENTORY.BASE}/hospital/${hospitalId}`);
  }

  /**
   * Get available inventory
   */
  getAvailableInventory(): Observable<BloodInventory[]> {
    // This endpoint might need to be added to the backend
    return this.get<BloodInventory[]>(`${API_ENDPOINTS.INVENTORY.BASE}/available`);
  }

  /**
   * Get expired inventory
   */
  getExpiredInventory(): Observable<BloodInventory[]> {
    // This endpoint might need to be added to the backend
    return this.get<BloodInventory[]>(`${API_ENDPOINTS.INVENTORY.BASE}/expired`);
  }

  /**
   * Add new inventory item
   */
  addInventoryItem(item: BloodInventory): Observable<string> {
    // This endpoint might need to be added to the backend
    return this.postText(`${API_ENDPOINTS.INVENTORY.BASE}/add`, item);
  }

  /**
   * Reserve inventory for request
   */
  reserveInventory(inventoryId: string, quantity: number): Observable<string> {
    // This endpoint might need to be added to the backend
    return this.putText(`${API_ENDPOINTS.INVENTORY.BASE}/reserve`, {
      inventoryId,
      quantity
    });
  }
}
