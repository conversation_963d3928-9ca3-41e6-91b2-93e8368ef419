<div id="wrapper" class="container">
  <div class="d-flex flex-column flex-md-row align-items-center justify-content-between mb-4">
    <h1>Donor History</h1>
  </div>

  <div class="table-responsive">
    <table id="keywords">
      <thead>
        <tr>
          <th><span>No</span></th>
          <th><span>RecordID</span></th>
          <th><span>HospitalID</span></th>
          <th><span>Donation Date</span></th>
          <th><span>Amount</span></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let listItem of historyList; index as i">
          <td>{{i+1}}</td>
          <td>{{listItem.recordID}}</td>
          <td>{{listItem.hospitalID}}</td>
          <td>{{listItem.donationDate}}</td>
          <td>{{listItem.amount}}</td>
      </tr> 
      </tbody>
    </table>
  </div>
</div>
