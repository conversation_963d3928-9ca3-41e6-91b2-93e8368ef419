import { APP_CONSTANTS } from '../constants';

/**
 * Local storage utility functions
 */

/**
 * Set item in localStorage with error handling
 */
export function setStorageItem(key: string, value: any): boolean {
  try {
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
    return true;
  } catch (error) {
    console.error('Error setting localStorage item:', error);
    return false;
  }
}

/**
 * Get item from localStorage with error handling
 */
export function getStorageItem<T>(key: string, defaultValue?: T): T | null {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue || null;
    }
    
    // Try to parse as JSON, if it fails return as string
    try {
      return JSON.parse(item);
    } catch {
      return item as unknown as T;
    }
  } catch (error) {
    console.error('Error getting localStorage item:', error);
    return defaultValue || null;
  }
}

/**
 * Remove item from localStorage
 */
export function removeStorageItem(key: string): boolean {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing localStorage item:', error);
    return false;
  }
}

/**
 * Clear all localStorage items
 */
export function clearStorage(): boolean {
  try {
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
}

/**
 * Check if localStorage is available
 */
export function isStorageAvailable(): boolean {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get user token from storage
 */
export function getUserToken(): string | null {
  return getStorageItem<string>(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN);
}

/**
 * Set user token in storage
 */
export function setUserToken(token: string): boolean {
  return setStorageItem(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN, token);
}

/**
 * Remove user token from storage
 */
export function removeUserToken(): boolean {
  return removeStorageItem(APP_CONSTANTS.STORAGE_KEYS.USER_TOKEN);
}

/**
 * Get user data from storage
 */
export function getUserData<T>(): T | null {
  return getStorageItem<T>(APP_CONSTANTS.STORAGE_KEYS.USER_DATA);
}

/**
 * Set user data in storage
 */
export function setUserData(userData: any): boolean {
  return setStorageItem(APP_CONSTANTS.STORAGE_KEYS.USER_DATA, userData);
}

/**
 * Remove user data from storage
 */
export function removeUserData(): boolean {
  return removeStorageItem(APP_CONSTANTS.STORAGE_KEYS.USER_DATA);
}
