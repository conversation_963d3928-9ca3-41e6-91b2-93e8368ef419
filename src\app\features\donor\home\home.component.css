.banner{
    background: #EF3B36;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to left, #FFFFFF, #EF3B36);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to left, #FFFFFF, #EF3B36); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

}
.circle-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: auto;
    padding: 60px;
    height: 600px;
}

.video {
    width: 100%;
    height: auto;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.detail {
    border-radius: 10px;
    position: absolute;
    width: 29%;
    text-align: center;
    color: #000000;
    padding: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 2;
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* Smooth transition for hover effects */
    border: 2px solid #FF416C; /* Black border */
}




/* Positioning details in a circular layout */
.detail1 { top: 5%; left: 50%; transform: translate(-50%, -50%); }
.detail2 { top: 20%; right: -20%; transform: translate(-50%, -50%); }
.detail3 { top: 70%; right: -20%; transform: translate(-50%, -50%); }
.detail6 { bottom: 30%; left: 10%; transform: translate(-50%, 50%); }
.detail7 { top: 50%; left: 0%; transform: translate(-50%, -50%); }
.detail8 { top: 20%; left: 10%; transform: translate(-50%, -50%); }
.detail9 { top: 50%; right: -30%; transform: translate(-50%, -50%); }
.detail10 { bottom: 5%; left: 50%; transform: translate(-50%, 50%); }

/* Responsive Adjustments */
@media (max-width: 768px) {
    .circle-container {
        height: 400px; /* Adjust circle container height for smaller screens */
        padding: 30px;
    }
    .detail {
        width: 60px; /* Smaller detail box size on smaller screens */
        padding: 5px;
    }
    /* Adjusted positions for details */
    .detail1 { top: 5%; left: 50%; transform: translate(-50%, -50%); }
    .detail2 { top: 20%; left: 50%; transform: translate(-50%, -50%); }
    .detail3 { top: 35%; left: 50%; transform: translate(-50%, -50%); }
    .detail6 { top: 50%; left: 50%; transform: translate(-50%, -50%); }
    .detail7 { top: 65%; left: 50%; transform: translate(-50%, -50%); }
    .detail8 { top: 80%; left: 50%; transform: translate(-50%, -50%); }
    .detail9 { top: 95%; left: 50%; transform: translate(-50%, -50%); }
    .detail10 { bottom: 5%; left: 50%; transform: translate(-50%, -50%); }
}


@media (max-width: 576px) {
    .circle-container {
        height: 300px; /* Further reduce height for very small screens */
    }
    .detail {
        width: 50px;
        padding: 5px;
        font-size: 0.8em; /* Smaller text on very small screens */
    }
}


.card{
    border:none;
    

    position:relative;
    overflow:hidden;
    border-radius:8px;
    cursor:pointer;
}
.idcolor{
    color: white;
    background: #c31432;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to left, #240b36, #c31432);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to left, #240b36, #c31432); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

}

.card:before{
    
    content:"";
    position:absolute;
    left:0;
    top:0;
    width:4px;
    height:100%;
    background-color:#E1BEE7;
    transform:scaleY(1);
    transition:all 0.5s;
    transform-origin: bottom
}

.card:after{
    
    content:"";
    position:absolute;
    left:0;
    top:0;
    width:4px;
    height:100%;
    background-color:#ff0000;
    transform:scaleY(0);
    transition:all 0.5s;
    transform-origin: bottom
}

.card:hover::after{
    transform:scaleY(1);
}


.fonts{
    font-size:11px;
}

.social-list{
    display:flex;
    list-style:none;
    justify-content:center;
    padding:0;
}

.social-list li{
    padding:10px;
    color:#8E24AA;
    font-size:19px;
}


.buttons button:nth-child(1){
       border:1px solid #8E24AA !important;
       color:#8E24AA;
       height:40px;
}

.buttons button:nth-child(1):hover{
       border:1px solid #8E24AA !important;
       color:#fff;
       height:40px;
       background-color:#8E24AA;
}

.buttons button:nth-child(2){
       border:1px solid #8E24AA !important;
       background-color:#8E24AA;
       color:#fff;
        height:40px;
}



.cover {
    background-image: url('/backdonar.jpg');
    background-size: cover;          /* Makes the image cover the entire element */
    background-position: center;     /* Centers the image */
    background-repeat: no-repeat;    /* Prevents the image from repeating */
    height: 100%;                   /* Optional: sets the height to full viewport height */
    width: 105vw;                     /* Optional: sets the width to 100% */
}
