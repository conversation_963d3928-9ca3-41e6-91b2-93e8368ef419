import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators,ReactiveFormsModule } from '@angular/forms';
import { AuthService } from '../../../core/services/auth.service';
import { LoginResponse } from '../../../models/auth.model';
import { Router, RouterLink } from '@angular/router';
import { APP_CONSTANTS } from '../../../constants';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule,RouterLink],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  loginForm: FormGroup;

  constructor(private fb: FormBuilder, private authService: AuthService, private router: Router) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(5)]]
    });
  }

  onLogin() {
    const { email, password } = this.loginForm.value; 
    this.authService.login(email, password).subscribe(
      (response: LoginResponse) => {
        console.log('Login successful', response);
        if (response.type === APP_CONSTANTS.USER_TYPES.ADMIN) {
          this.router.navigate([APP_CONSTANTS.ROUTES.ADMIN],{ queryParams: { id: response.id } });
        }else if(response.type === APP_CONSTANTS.USER_TYPES.DONOR) {
          this.router.navigate([APP_CONSTANTS.ROUTES.DONOR_HOME],{ queryParams: { id: response.id } });
        }else if(response.type === APP_CONSTANTS.USER_TYPES.HOSPITAL) {
          this.router.navigate([APP_CONSTANTS.ROUTES.HOSPITAL],{ queryParams: { id: response.id } });
        }
      },
      error => {
        Swal.fire({
          title: 'Error!',
          text: 'Login failed. Please check your credentials.',
          icon: 'error',
          confirmButtonText: 'Try Again'
        });
      }
    );
  }
  onLogout() {
    this.authService.logout();
    this.router.navigate([APP_CONSTANTS.ROUTES.LOGIN]); // Navigate to login page after logout
  }
}