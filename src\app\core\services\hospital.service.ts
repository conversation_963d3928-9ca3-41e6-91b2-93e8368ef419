import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseHttpService } from './base-http.service';
import { Hospital, BloodRequest, ApproveRequest } from '../../models';
import { API_ENDPOINTS } from '../../constants';

@Injectable({
  providedIn: 'root'
})
export class HospitalService extends BaseHttpService {

  /**
   * Get hospital by ID
   */
  getHospitalById(id: string): Observable<Hospital> {
    return this.get<Hospital>(API_ENDPOINTS.HOSPITAL.FIND_BY_ID(id));
  }

  /**
   * Add new hospital
   */
  addHospital(hospital: Hospital): Observable<string> {
    return this.postText(API_ENDPOINTS.HOSPITAL.ADD, hospital);
  }

  /**
   * Update hospital information
   */
  updateHospital(hospital: Partial<Hospital>): Observable<string> {
    return this.putText(API_ENDPOINTS.HOSPITAL.UPDATE, hospital);
  }

  /**
   * Delete hospital
   */
  deleteHospital(id: string): Observable<string> {
    return this.delete<string>(API_ENDPOINTS.HOSPITAL.DELETE(id));
  }

  /**
   * Get all hospitals
   */
  getAllHospitals(): Observable<Hospital[]> {
    return this.get<Hospital[]>(API_ENDPOINTS.HOSPITAL.BASE);
  }

  /**
   * Create blood request
   */
  createBloodRequest(request: BloodRequest): Observable<string> {
    return this.postText(API_ENDPOINTS.BLOOD_REQUEST.ADD, request);
  }

  /**
   * Get pending blood requests
   */
  getPendingBloodRequests(): Observable<BloodRequest[]> {
    return this.get<BloodRequest[]>(API_ENDPOINTS.BLOOD_REQUEST.PENDING);
  }

  /**
   * Get completed blood requests
   */
  getCompletedBloodRequests(): Observable<BloodRequest[]> {
    return this.get<BloodRequest[]>(API_ENDPOINTS.BLOOD_REQUEST.COMPLETED);
  }

  /**
   * Get approved blood requests
   */
  getApprovedBloodRequests(): Observable<BloodRequest[]> {
    return this.get<BloodRequest[]>(API_ENDPOINTS.BLOOD_REQUEST.APPROVED);
  }

  /**
   * Create approve request
   */
  createApproveRequest(request: ApproveRequest): Observable<string> {
    return this.postText(API_ENDPOINTS.APPROVE_REQUEST.ADD, request);
  }

  /**
   * Get completed approve requests
   */
  getCompletedApproveRequests(): Observable<ApproveRequest[]> {
    return this.get<ApproveRequest[]>(API_ENDPOINTS.APPROVE_REQUEST.COMPLETED);
  }

  /**
   * Get hospital blood requests by hospital ID
   */
  getHospitalBloodRequests(hospitalId: string): Observable<BloodRequest[]> {
    // This endpoint might need to be added to the backend
    return this.get<BloodRequest[]>(`${API_ENDPOINTS.BLOOD_REQUEST.BASE}/hospital/${hospitalId}`);
  }
}
