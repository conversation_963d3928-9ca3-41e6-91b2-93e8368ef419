<div id="wrapper" class="container">
    <div class="d-flex flex-column flex-md-row align-items-center justify-content-between mb-4">
        <h1>Appointment</h1>
        <button class="btn btn-outline-danger mt-3 mt-md-0 " data-bs-toggle="modal" data-bs-target="#staticBackdrop"
            (click)="addedAppointment()">Make An Appointment</button>
    </div>

    <!-- Modal -->
 <!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
aria-labelledby="staticBackdropLabel" aria-hidden="true">
<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h1 class="modal-title fs-5 " id="staticBackdropLabel">Make An Appointment</h1>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="mb-1">
                <label for="donorID" class="col-form-label">DonarID</label>
                <input type="text" [disabled]="true" class="form-control"  
                    [(ngModel)]="donarID"  name="donorID"/>
            </div>
            <div class="mb-1">
                <label for="name" class="col-form-label">Name</label>
                <input type="text" class="form-control" 
                    [(ngModel)]="name" name="name" required />
            </div>
            <div class="mb-1">
                <label for="email" class="col-form-label">Email</label>
                <input type="email" class="form-control"  
                    [(ngModel)]="email" name="email" required />
            </div>
            <div class="mb-1">
                <label for="contactNumber" class="col-form-label">Contact Number</label>
                <input type="text" class="form-control" 
                    [(ngModel)]="contactNumber" name="contactNumber" required />
            </div>
            <div class="mb-1">
                <label for="address" class="col-form-label">Address</label>
                <input type="text" class="form-control"  
                    [(ngModel)]="address" name="address"  required />
            </div>
            <div class="mb-1">
                <label for="bloodType" class="col-form-label">Blood Type</label>
                <input type="text" class="form-control" 
                    [(ngModel)]="bloodGroup"  name="bloodGroup" required />
            </div>
            <div class="mb-1">
                <label for="preferDate" class="col-form-label">Prefer Date</label>
                <input type="date" class="form-control"  
                    [(ngModel)]="preferDate" name="preferDate" />
            </div>
            <div class="mb-1">
                <label for="remarks" class="col-form-label">Remarks</label>
                <input type="text" class="form-control"
                    [(ngModel)]="remarks" name="remarks" />
            </div>
            <div class="mb-1">
                <label for="status" class="col-form-label">Status</label>
                <input [disabled]="true" type="text" class="form-control" 
                     required value="PENDING"  />
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" (click)="addAppointment()">Make</button>
        </div>
    </div>
</div>
</div>


    <div class="table-responsive">
        <table id="keywords">
            <thead>
                <tr>
                    <th><span>NO</span></th>
                    <th><span>AppointmentID</span></th>
                    <th><span>BloodType</span></th>
                    <th><span>DonationDate</span></th>
                    <th><span>DonationLocation</span></th>
                    <th><span>Remarks</span></th>
                    <th><span>Statuse</span></th>
                    <th><span>Delete</span></th>
                    <th><span>Update</span></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let Appointment of appoitmentsList; index as i">
                    <td>{{i+1}}</td>
                    <td>{{Appointment.appointmentID}}</td>
                    <td>{{Appointment.bloodType}}</td>
                    <td>{{Appointment.preferDate}}</td>
                    <td>{{Appointment.address}}</td>
                    <td>{{Appointment.remarks}}</td>
                    <td>{{Appointment.status}}</td>
                    <td><button class="btn btn-danger" (click)="deleteAppointmentById(Appointment.appointmentID)"><i
                                class="bi bi-trash-fill"></i></button></td>
                                <td>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateAppointmentModal"
                                (click)="updateAppointment(Appointment)">
                            <i class="bi bi-pencil-square"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

</div>


<!-- Update Appointment Modal -->
<div class="modal fade" id="updateAppointmentModal" tabindex="-1" aria-labelledby="updateAppointmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateAppointmentModalLabel">Update Appointment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="bloodType" class="form-label">Blood Type</label>
                        <input type="text" class="form-control" name="bloodType"  [(ngModel)]="AptTemp.bloodType"  required>
                    </div>
                    <div class="mb-3">
                        <label for="preferDate" class="form-label">Preferred Date</label>
                        <input type="date" class="form-control" name="preferDate"  [(ngModel)]="AptTemp.preferDate"  required>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <input type="text" class="form-control" name="address" [(ngModel)]="AptTemp.address"  required>
                    </div>
                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" name="remarks"  [(ngModel)]="AptTemp.remarks"  required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="extraField" class="form-label">Extra Field</label>
                        <input type="text" class="form-control" name="extraField"  [(ngModel)]="AptTemp.extraField" >
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" >Save changes</button>
            </div>
        </div>
    </div>
</div>

