<div class="container-fluid " style="padding-left: 0px;padding-right: 0px;">
  <div class="row cover">
    <div class="container my-5">
      <div class="circle-container position-relative mx-auto">
        <video class="video" autoplay loop muted>
          <source src="/heart.webm" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        <div class="detail detail1 ">
          <small>Age</small>
          <h4>16+ years</h4>
      </div>
      <div class="detail detail2">
          <small>Weight</small>
          <h4>110 lbs+</h4>
      </div>
      <div class="detail detail3">
          <small>Health Status</small>
          <h4>Good health</h4>
      </div>
      <div class="detail detail6">
          <small>Medical History</small>
          <h4>Recent check</h4>
      </div>
      <div class="detail detail7">
          <small>Last Donation Date</small>
          <h4>56 days</h4>
      </div>
      <div class="detail detail8">
          <small>Lifestyle Factors</small>
          <h4>No tattoos</h4>
      </div>
      <div class="detail detail9">
          <small>Medications</small>
          <h4>Inform staff</h4>
      </div>
      <div class="detail detail10">
          <small>Pregnancy</small>
          <h4>No Details</h4>
      </div>
      

        
      </div>
    </div>
    <div class="text-center fw-semibold">
      <h6>Good health is the foundation of a vibrant life. It's not just the absence of illness but a state of complete 
          <strong class="text-decoration-underline">physical, mental, social well-being</strong>.
      </h6>
  </div>
  
  
  </div>




  <div class="row banner">
    <div class="div d-flex justify-content-center text-center">
      <h1 class="text-white fw-bolder ">Informations</h1>
    </div>

    <div class="container-fluid" data-aos="fade-up">
      <div class="row">
        <div class="container mt-1 mb-3">
          <div class="row d-flex justify-content-center">
            <div class="col-md-7">
              <div class="card border border-danger p-1 py-4">
                <!-- Added border and border-dark classes -->
                <div class="text-center">
                  <img src="https://i.imgur.com/bDLhJiP.jpg" width="100" class="rounded-circle" />
                </div>

                <div class="text-center mt-3">
                  <span class="idcolor p-1 px-4 rounded text-white">Donor ID {{donar.donorID}}</span>
                  <h5 class="mt-2 mb-0 fw-bold">Name : {{ donar.name }}</h5>
                  <span class="fw-bold">Address : {{ donar.address }} , </span>
                  <span class="fw-bold">{{ donar.city }}</span>

                  <div class="px-4 mt-1 ">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <label for="emailAddress" class="fw-semibold">Email Address:</label>
                      <h6 class="mb-0 fw-semibold">{{ donar.email }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <label for="contactNumber" class="fw-semibold">Contact Number:</label>
                      <h6 class="mb-0 fw-semibold">{{ donar.contactNumber }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <label for="bloodGroup" class="fw-semibold">Blood Group:</label>
                      <h6 class="mb-0 fw-semibold">{{ donar.bloodGroup }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <label for="dob" class="fw-semibold">DOB:</label>
                      <h6 class="mb-0 fw-semibold">{{ donar.dob }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <label for="gender" class="fw-semibold">Gender:</label>
                      <h6 class="mb-0 fw-semibold">{{ donar.gender }}</h6>
                    </div>
                  </div>

                  <div class="buttons">
                    <button type="button" class="btn btn-outline-primary fw-bold" data-bs-toggle="modal"
                      data-bs-target="#exampleModal" (click)="updateDonarDetails(donar)">
                      Edit
                    </button>

                    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel"
                      aria-hidden="true">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalLabel">
                              Update Your Details
                            </h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            <form>
                              <div class="mb-1">
                                <label for="donorID" class="col-form-label">DonarID</label>
                                <input disabled type="text" class="form-control" [(ngModel)]="updateDonar.donorID"
                                  required value="{{ donar.donorID }}" name="donarID" />
                              </div>
                              <div class="mb-1">
                                <label for="name" class="col-form-label">Name</label>
                                <input type="text" class="form-control" [(ngModel)]="updateDonar.name" required
                                  value="{{ donar.name }}" name="name" />
                              </div>
                              <div class="mb-1">
                                <label for="address" class="col-form-label">Donar Address</label>
                                <input type="text" class="form-control" [(ngModel)]="updateDonar.address" required
                                  value="{{ donar.address }}" name="address" />
                              </div>
                              <div class="mb-1">
                                <label for="city" class="col-form-label">Donar City</label>
                                <input type="text" class="form-control" [(ngModel)]="updateDonar.city" required
                                  value="{{ donar.city }}" name="city" />
                              </div>
                              <div class="mb-1">
                                <label for="email" class="col-form-label">Email</label>
                                <input type="email" class="form-control" [(ngModel)]="updateDonar.email" required
                                  value="{{ donar.email }}" name="email" />
                              </div>
                              <div class="mb-1">
                                <label for="contactNumber" class="col-form-label">Contact Number</label>
                                <input type="text" class="form-control" [(ngModel)]="updateDonar.contactNumber" required
                                  value="{{ donar.contactNumber }}" name="contactNumber" />
                              </div>
                              <div class="mb-1">
                                <label for="bloodGroup" class="col-form-label">Blood Group</label>
                                <input type="text" class="form-control" [(ngModel)]="updateDonar.bloodGroup" required
                                  value="{{ donar.bloodGroup }}" name="bloodGroup" />
                              </div>
                              <div class="mb-1">
                                <label for="age" class="col-form-label">Age</label>
                                <input type="number" class="form-control" [(ngModel)]="updateDonar.age" required
                                  value="{{ donar.age }}" name="age" />
                              </div>
                            </form>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                              Close
                            </button>
                            <button type="submit" class="btn btn-primary" (click)="saveDonar()">
                              Save Changes
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <button class="btn btn-outline-danger px-4 ms-3 fw-bold" (click)="deleteDonar()">
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row pb-3 pt-3 banner">
    <app-appointments [donId]="id"></app-appointments>
  </div>
  <div class="row pb-3 banner">
    <app-donar-history [id]="id"></app-donar-history>
  </div>
</div>