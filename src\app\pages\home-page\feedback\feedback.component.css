.feedback-container {
    position: relative;
    padding: 20px;  /* Padding inside the container */
    border-radius: 1rem;  /* Rounded corners */
    background-color: rgb(255, 255, 255);  /* Background color */
    width: 80%;  /* Ensures it takes up full width */
    max-width: 100%;  /* Prevents exceeding full width */
}

.feedback-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;  /* Place behind the content */
    border-radius: 1rem;
    background: linear-gradient(30deg, #f06957, #ff4157);  /* Gradient color */
    padding: 5px;  /* Padding for the border */
    filter: blur(25px);  /* Optional blur effect */
}

/* Adjustments for responsiveness */
@media (max-width: 768px) {
    .feedback-container {
        padding: 15px;  /* Reduce padding on smaller screens */
    }
    .feedback-container h1 {
        font-size: 1.5rem;  /* Adjust font size for smaller screens */
    }
    .feedback-container h2 {
        font-size: 1.2rem;  /* Adjust font size for smaller screens */
    }
}
